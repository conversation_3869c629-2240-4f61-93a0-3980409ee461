-- Migration script to remove family leave and update exceptional leave rules
-- Run this script to update the existing database schema and data

-- Step 1: Remove all family leave data from leave_balances table
DELETE FROM leave_balances WHERE leave_type = 'familial';

-- Step 2: Remove all family leave data from leave_accrual_history table
DELETE FROM leave_accrual_history WHERE leave_type = 'familial';

-- Step 3: Update any existing family leave requests to be rejected with explanation
UPDATE demandes_conges
SET statut = 'refusee',
    motif_rejet = 'Type de congé familial supprimé du système - contactez les RH pour plus d\'informations',
    date_decision = NOW()
WHERE type = 'familial' AND statut IN ('en_attente_responsable', 'en_attente_planificateur');

-- Step 4: Add a comment to existing approved/rejected family leave requests for historical tracking
UPDATE demandes_conges
SET motif = CONCAT(IFNULL(motif, ''), ' [HISTORIQUE: Congé familial - type supprimé du système]')
WHERE type = 'familial' AND statut IN ('approuvee', 'refusee');

-- Step 5: Remove exceptional leave balance tracking by deleting all exceptional leave balance records
-- This implements the new rule where exceptional leave doesn't have a balance counter
DELETE FROM leave_balances WHERE leave_type = 'exceptionnel';

-- Step 6: Remove exceptional leave accrual history since we're not tracking balances anymore
DELETE FROM leave_accrual_history WHERE leave_type = 'exceptionnel';

-- Step 7: Update the enum in leave_accrual_history table to remove 'familial'
ALTER TABLE leave_accrual_history
MODIFY COLUMN leave_type enum('paye','exceptionnel','sans solde','maladie') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;

-- Step 8: Update the enum in leave_balances table to remove 'familial'
ALTER TABLE leave_balances
MODIFY COLUMN leave_type enum('paye','exceptionnel','sans solde','maladie') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL;

-- Verification queries (uncomment to run after migration)
-- SELECT COUNT(*) as family_leave_balances FROM leave_balances WHERE leave_type = 'familial';
-- SELECT COUNT(*) as family_leave_history FROM leave_accrual_history WHERE leave_type = 'familial';
-- SELECT COUNT(*) as exceptional_leave_balances FROM leave_balances WHERE leave_type = 'exceptionnel';
-- SELECT COUNT(*) as family_leave_requests FROM demandes_conges WHERE type = 'familial';

-- Note: This migration is irreversible. Make sure to backup your database before running.
