-- Migration script to implement rolling 3-day exceptional leave balance system
-- This replaces the 48-hour cooldown with a rolling balance that resets every 48 hours

-- Step 1: Create a new table to track exceptional leave balances with reset times
CREATE TABLE IF NOT EXISTS exceptional_leave_balances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    current_balance DECIMAL(3,1) NOT NULL DEFAULT 3.0,
    last_reset_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_balance (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Step 2: Initialize exceptional leave balances for all existing users
INSERT INTO exceptional_leave_balances (user_id, current_balance, last_reset_time)
SELECT 
    id as user_id,
    3.0 as current_balance,
    NOW() as last_reset_time
FROM users
ON DUPLICATE KEY UPDATE
    current_balance = 3.0,
    last_reset_time = NOW();

-- Step 3: Create an index for efficient queries
CREATE INDEX idx_exceptional_balance_user_reset ON exceptional_leave_balances(user_id, last_reset_time);

-- Verification queries (uncomment to run after migration)
-- SELECT COUNT(*) as users_with_exceptional_balance FROM exceptional_leave_balances;
-- SELECT user_id, current_balance, last_reset_time FROM exceptional_leave_balances LIMIT 5;

-- Note: This migration adds the new rolling balance system while preserving existing exceptional leave request history
