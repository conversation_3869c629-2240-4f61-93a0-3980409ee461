<?php
/**
 * <PERSON>rip<PERSON> to remove exceptional leave balance and family leave references from DemandeController
 * This script will clean up all the view data arrays to remove these references
 */

$controllerFile = __DIR__ . '/../app/controllers/DemandeController.php';

if (!file_exists($controllerFile)) {
    echo "Controller file not found: $controllerFile\n";
    exit(1);
}

$content = file_get_contents($controllerFile);

// Remove exceptional leave balance references
$patterns = [
    // Remove exceptional leave balance lines (with comma)
    "/\s*'solde_exceptionnel' => \\\$leaveBalances\['exceptionnel'\]\['remaining'\],\s*\n/",
    "/\s*'solde_exceptionnel_used' => \\\$leaveBalances\['exceptionnel'\]\['used'\],\s*\n/",
    "/\s*'solde_exceptionnel_total' => \\\$leaveBalances\['exceptionnel'\]\['total'\],\s*\n/",

    // Remove family leave balance lines (with comma)
    "/\s*'solde_familial' => \\\$leaveBalances\['familial'\]\['remaining'\],\s*\n/",
    "/\s*'solde_familial_used' => \\\$leaveBalances\['familial'\]\['used'\],\s*\n/",
    "/\s*'solde_familial_total' => \\\$leaveBalances\['familial'\]\['total'\],\s*\n/",

    // Remove standalone exceptional leave references (without comma)
    "/\s*'solde_exceptionnel' => \\\$leaveBalances\['exceptionnel'\]\['remaining'\]\s*\n/",

    // Remove empty lines that might be left behind
    "/\n\s*\n\s*\n/",
];

$replacements = [
    '', '', '', '', '', '', '', "\n\n"
];

$content = preg_replace($patterns, $replacements, $content);

// Write the updated content back to the file
if (file_put_contents($controllerFile, $content) !== false) {
    echo "Successfully updated DemandeController.php\n";
    echo "Removed exceptional leave balance and family leave references\n";
} else {
    echo "Failed to update DemandeController.php\n";
    exit(1);
}
?>
