# Leave Management System Changes Summary

## Overview
This document summarizes the changes made to implement new rules for exceptional leave and remove family leave from the gestion-conges system.

## Changes Implemented

### 1. Exceptional Leave Changes
**Old System**: Exceptional leave used balance tracking with remaining days counter
**New System**: Exceptional leave uses a 48-hour cooldown rule

#### Database Changes:
- Removed all exceptional leave balance records from `leave_balances` table
- Removed all exceptional leave accrual history from `leave_accrual_history` table
- Exceptional leave no longer tracks balances or accruals

#### Business Logic Changes:
- **48-Hour Rule**: Users cannot request exceptional leave if they have used exceptional leave in the previous 48 hours
- **3-Day Limit**: Maintained existing 3-day maximum limit per request
- **Balance Bypass**: Exceptional leave requests bypass balance checking entirely

#### Code Changes:
- `LeaveBalanceModel::getUserLeaveBalances()`: Returns 'N/A' for exceptional leave balances with 48-hour rule status
- `LeaveBalanceModel::canRequestExceptionalLeave()`: Checks for recent exceptional leave usage
- `LeaveBalanceModel::hasSufficientBalance()`: Bypasses balance check for exceptional leave
- `DemandeController::nouvelle()`: Updated validation to use 48-hour rule instead of balance check

### 2. Family Leave Removal
**Action**: Completely removed family leave ("familial") from the entire system

#### Database Changes:
- Deleted all family leave balance records from `leave_balances` table
- Deleted all family leave accrual history from `leave_accrual_history` table
- Updated pending family leave requests to "rejected" status with explanation
- Added historical notes to existing approved/rejected family leave requests

#### Code Changes:
- Removed family leave from all dropdown menus and forms
- Updated `DemandeModel::formatLeaveType()` to remove family leave handling
- Removed family leave from filter options in all views
- Updated `ApiController::formatLeaveType()` to remove family leave
- Removed family leave from admin leave accrual history display

### 3. User Interface Updates

#### Forms and Dropdowns:
- `app/views/demandes/nouvelle.php`: Updated leave type dropdown, removed family leave
- `app/views/responsable/demandes_approbation.php`: Removed family leave from filter dropdown
- Enhanced exceptional leave option with "(max 3 jours, règle 48h)" description

#### Dashboard Updates:
- `app/views/employe/dashboard.php`: Updated to show exceptional leave status with 48-hour rule
- Displays "Disponible" or "Indisponible" based on 48-hour rule
- Shows last exceptional leave usage date
- Explains 48-hour cooldown rule

#### Validation Updates:
- Updated leave request validation to skip balance check for exceptional leave
- Enhanced error messages to explain 48-hour rule violations
- Maintained existing overlap detection and other validation rules

### 4. Files Modified

#### Models:
- `app/models/LeaveBalanceModel.php`: Updated balance handling and added 48-hour rule methods
- `app/models/DemandeModel.php`: Removed family leave references from type filters

#### Controllers:
- `app/controllers/DemandeController.php`: Updated validation logic for exceptional leave
- `app/controllers/ApiController.php`: Removed family leave formatting

#### Views:
- `app/views/demandes/nouvelle.php`: Updated form dropdown
- `app/views/employe/dashboard.php`: Updated leave balance display
- `app/views/responsable/demandes_approbation.php`: Updated filter dropdown
- `app/views/admin/leave_accrual_history.php`: Removed family leave display

#### Database:
- `sql/migrate_remove_family_leave.sql`: Migration script for database changes

### 5. Validation Rules Summary

#### Exceptional Leave Rules:
1. **48-Hour Cooldown**: Cannot request if used exceptional leave in last 48 hours
2. **3-Day Maximum**: Maximum 3 days per request (unchanged)
3. **No Balance Tracking**: No longer uses balance/accrual system
4. **Status Check**: Checks against approved, pending responsable, and pending planificateur requests

#### Family Leave Rules:
- **Completely Removed**: No longer available as an option
- **Historical Data**: Existing requests marked with historical notes
- **Pending Requests**: Automatically rejected with explanation

### 6. Testing Recommendations

#### Test Exceptional Leave 48-Hour Rule:
1. Submit and approve an exceptional leave request
2. Try to submit another exceptional leave request within 48 hours (should be blocked)
3. Wait 48+ hours and try again (should be allowed)
4. Verify dashboard shows correct status

#### Test Family Leave Removal:
1. Verify family leave option is not available in any dropdown
2. Check that existing family leave data is properly handled
3. Ensure no errors occur when viewing historical family leave requests

#### Test Balance Display:
1. Verify exceptional leave shows "N/A" for balance fields
2. Check that 48-hour rule status is correctly displayed
3. Ensure other leave types still show proper balances

### 7. Data Integrity

#### Preserved:
- All historical leave request data (with appropriate notes)
- Existing balance tracking for paid leave, unpaid leave, and sick leave
- User profiles and other system data

#### Removed:
- Exceptional leave balance tracking
- Family leave balance tracking
- Family leave accrual history
- Exceptional leave accrual history

#### Updated:
- Pending family leave requests → rejected with explanation
- Exceptional leave validation logic → 48-hour rule based

## Migration Status
✅ Database migration completed successfully
✅ Code changes implemented
✅ UI updates completed
✅ Validation logic updated
✅ Historical data preserved with appropriate notes
✅ **BUG FIX**: Fixed undefined variable `$leaveBalanceModel` in DemandeController

## Bug Fix Details
**Issue**: `Warning: Undefined variable $leaveBalanceModel` and `Fatal error: Call to a member function canRequestExceptionalLeave() on null`

**Root Cause**: In the `DemandeController::nouvelle()` method, some calls used `$leaveBalanceModel` instead of `$this->leaveBalanceModel`

**Files Fixed**:
- `app/controllers/DemandeController.php` (lines 334, 335, 366, 635, 636)

**Changes Made**:
- Line 334: `$leaveBalanceModel->canRequestExceptionalLeave()` → `$this->leaveBalanceModel->canRequestExceptionalLeave()`
- Line 335: `$leaveBalanceModel->getLastExceptionalLeaveEndDate()` → `$this->leaveBalanceModel->getLastExceptionalLeaveEndDate()`
- Line 366: `$leaveBalanceModel->hasSufficientBalance()` → `$this->leaveBalanceModel->hasSufficientBalance()`
- Line 635: `$leaveBalanceModel->canRequestExceptionalLeave()` → `$this->leaveBalanceModel->canRequestExceptionalLeave()`
- Line 636: `$leaveBalanceModel->getLastExceptionalLeaveEndDate()` → `$this->leaveBalanceModel->getLastExceptionalLeaveEndDate()`

## Notes
- The migration is irreversible - family leave functionality cannot be restored without data loss
- Exceptional leave 48-hour rule is based on the end date of the last exceptional leave request
- All changes maintain backward compatibility for existing approved leave requests
- System continues to function normally for all other leave types
- **System is now fully functional** - the undefined variable error has been resolved
