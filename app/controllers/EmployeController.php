<?php
// Employe Controller

class EmployeController extends Controller {
    private $documentModel;

    public function __construct() {
        // Check if user is logged in and is an employe
        Auth::requireRole('employe');

        // Load models
        $this->documentModel = new DocumentModel();
    }

    /**
     * Format status for display
     * @param string $statut Database status value
     * @return string Formatted status for display
     */
    private function formatStatut($statut) {
        switch (strtolower($statut)) {
            case 'en cours':
                return 'En attente';
            case 'acceptée':
                return 'Approuvée';
            case 'refusée':
                return 'Refusée';
            default:
                return 'Traitée';
        }
    }

    /**
     * Count leave requests submitted in the current month
     * @param DemandeModel $demandeModel The demande model
     * @param int $userId The user ID
     * @return int Number of requests in current month
     */
    private function countCurrentMonthRequests($demandeModel, $userId) {
        $allUserDemandes = $demandeModel->getDemandesForUser($userId);
        $count = 0;
        $currentMonth = date('m');
        $currentYear = date('Y');

        foreach ($allUserDemandes as $demande) {
            $demandeMonth = date('m', strtotime($demande['date_demande']));
            $demandeYear = date('Y', strtotime($demande['date_demande']));

            if ($demandeMonth == $currentMonth && $demandeYear == $currentYear) {
                $count++;
            }
        }

        return $count;
    }

    public function dashboard() {
        // Load DemandeModel to access leave request data
        $demandeModel = new DemandeModel();

        // Load DocumentModel to access internal documents
        $documentModel = new DocumentModel();

        // Get recent leave requests from the database (limit to 2)
        $demandesBrutes = $demandeModel->getRecentHistory($_SESSION['user_id'], 2);

        // Format the demandes to match what the view expects (type, date, statut)
        $demandesRecentes = [];
        foreach ($demandesBrutes as $demande) {
            $demandesRecentes[] = [
                'type' => $demandeModel->formatLeaveType($demande['type']),
                'date' => date('d/m/Y', strtotime($demande['date_demande'])),
                'statut' => $this->formatStatut($demande['statut']),
                'id' => $demande['id'] // Adding ID for possible linking
            ];
        }

        // Get latest demande info if available
        $derniereDemande = !empty($demandesBrutes) ? $demandesBrutes[0] : null;
        $derniereDemandeDate = $derniereDemande ? date('d/m/Y', strtotime($derniereDemande['date_demande'])) : 'N/A';
        $derniereDemandeType = $derniereDemande ? $demandeModel->formatLeaveType($derniereDemande['type']) : 'N/A';

        // Get user's leave balances using the LeaveBalanceModel
        $leaveBalanceModel = new LeaveBalanceModel();
        $leaveBalances = $leaveBalanceModel->getUserLeaveBalances($_SESSION['user_id']);

        // Get next accrual date (first day of next month)
        $nextMonth = new DateTime('first day of next month');
        $nextAccrualDate = $nextMonth->format('Y-m-d');

        // Get user's role to determine accrual rate
        $userModel = new UserModel();
        $user = $userModel->getUserById($_SESSION['user_id']);
        $accrualRate = 1.4; // Default for regular employees

        if ($user['role'] === 'responsable') {
            $accrualRate = 1.83;
        } elseif ($user['role'] === 'admin') {
            $accrualRate = 1.99;
        }

        // Get department leave balance information
        $departmentLeaveBalanceModel = new DepartmentLeaveBalanceModel();
        $departmentBalance = $departmentLeaveBalanceModel->getDepartmentLeaveBalance($user['departement']);

        // Get monthly statistics for the user
        $monthlyStats = $demandeModel->getMonthlyStatistics($_SESSION['user_id']);

        // Get the team statistics
        $teamStats = null;
        if (!empty($user['departement'])) {
            $teamStats = $demandeModel->getDepartmentAbsenceData($user['departement']);
        }

        // Get internal documents from the database
        $documentsInternes = $documentModel->getAllDocuments();

        // Prepare data for the view
        $data = [
            'derniereDemandeDate' => $derniereDemandeDate,
            'derniereDemandeType' => $derniereDemandeType,
            'congesDisponibles' => $leaveBalances['payé']['remaining'],

            // Detailed leave balance information
            'solde_paye' => $leaveBalances['payé']['remaining'],
            'solde_paye_used' => $leaveBalances['payé']['used'],
            'solde_paye_total' => $leaveBalances['payé']['total'],

            // Exceptional leave validation data (no balance tracking)
            'can_request_exceptional' => $leaveBalanceModel->canRequestExceptionalLeave($_SESSION['user_id']),
            'last_exceptional_date' => $leaveBalanceModel->getLastExceptionalLeaveEndDate($_SESSION['user_id']),

            'solde_sans_solde' => $leaveBalances['sans solde']['remaining'],
            'solde_sans_solde_used' => $leaveBalances['sans solde']['used'],
            'solde_sans_solde_total' => $leaveBalances['sans solde']['total'],

            // Accrual information
            'next_accrual_date' => $nextAccrualDate,
            'accrual_rate' => $accrualRate,

            // Department balance information
            'departement' => $user['departement'],
            'departement_solde_total' => $departmentBalance ? $departmentBalance['total_days'] : 0,
            'departement_solde_used' => $departmentBalance ? $departmentBalance['used_days'] : 0,
            'departement_solde_remaining' => $departmentBalance ? $departmentBalance['remaining_days'] : 0,
            'departement_stats' => $teamStats,

            'demandesRecentes' => $demandesRecentes,
            'absencesAVenir' => $demandeModel->getUpcomingLeaves($_SESSION['user_id'], 3),
            'notificationsImportantes' => [
                ['message' => 'Votre demande de congé du 05/04/2025 est en attente d\'approbation.'],
            ],
            // Get user statistics from the database for the remaining fields
            'demandesSoumisesCeMois' => $this->countCurrentMonthRequests($demandeModel, $_SESSION['user_id']),
            'pourcentageDemandesApprouvees' => $demandeModel->getUserStatistics($_SESSION['user_id'])['approval_rate'],
            'monthlyStats' => $monthlyStats,

            // Internal documents from database
            'documentsInternes' => $documentsInternes
        ];

        $this->view('employe/dashboard', $data);
    }
}
