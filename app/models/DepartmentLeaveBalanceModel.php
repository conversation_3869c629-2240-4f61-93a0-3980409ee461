<?php
// Model for handling department leave balance operations

class DepartmentLeaveBalanceModel extends Model {

    /**
     * Initialize department leave balances
     *
     * @param string $departmentName The department name
     * @param int $year The year (defaults to current year)
     * @param float $totalDays The total days to allocate (defaults to 15)
     * @return bool Success or failure
     */
    public function initializeDepartmentLeaveBalance($departmentName, $year = null, $totalDays = 15) {
        if ($year === null) {
            $year = date('Y');
        }

        try {
            $stmt = $this->db->prepare("
                INSERT INTO department_leave_balances (
                    department_name,
                    year,
                    total_days,
                    used_days,
                    remaining_days
                ) VALUES (
                    ?,
                    ?,
                    ?,
                    0,
                    ?
                ) ON DUPLICATE KEY UPDATE
                    total_days = VALUES(total_days),
                    remaining_days = VALUES(total_days) - used_days
            ");

            return $stmt->execute([$departmentName, $year, $totalDays, $totalDays]);
        } catch (PDOException $e) {
            error_log("Error initializing department leave balance: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Initialize leave balances for all departments
     *
     * @param int $year The year (defaults to current year)
     * @param float $totalDays The total days to allocate (defaults to 15)
     * @return bool Success or failure
     */
    public function initializeAllDepartmentLeaveBalances($year = null, $totalDays = 15) {
        if ($year === null) {
            $year = date('Y');
        }

        try {
            // Get all departments
            $userModel = new UserModel();
            $departments = $userModel->getAllDepartments();

            if (isset($departments['all'])) {
                unset($departments['all']); // Remove the "all" option
            }

            $success = true;
            foreach ($departments as $department) {
                $result = $this->initializeDepartmentLeaveBalance($department, $year, $totalDays);
                if (!$result) {
                    $success = false;
                }
            }

            return $success;
        } catch (Exception $e) {
            error_log("Error initializing all department leave balances: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get department leave balance
     *
     * @param string $departmentName The department name
     * @param int $year The year (defaults to current year)
     * @return array|bool Department balance data or false on failure
     */
    public function getDepartmentLeaveBalance($departmentName, $year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        try {
            // Check if department has initialized balance for the year
            $checkStmt = $this->db->prepare("
                SELECT COUNT(*) as count
                FROM department_leave_balances
                WHERE department_name = ? AND year = ?
            ");
            $checkStmt->execute([$departmentName, $year]);
            $count = $checkStmt->fetch()['count'];

            // If no balance exists, initialize it
            if ($count == 0) {
                $this->initializeDepartmentLeaveBalance($departmentName, $year);
            }

            // Get the balance
            $stmt = $this->db->prepare("
                SELECT
                    total_days,
                    used_days,
                    remaining_days
                FROM department_leave_balances
                WHERE department_name = ? AND year = ?
            ");
            $stmt->execute([$departmentName, $year]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error fetching department leave balance: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if department has sufficient leave balance
     *
     * @param string $departmentName The department name
     * @param float $requestedDays The number of days requested
     * @param string $leaveType The type of leave
     * @param int $year The year (defaults to current year)
     * @return bool True if sufficient balance, false otherwise
     */
    public function hasSufficientDepartmentBalance($departmentName, $requestedDays, $leaveType, $year = null) {
        // Special leave types bypass the department balance check
        if (in_array($leaveType, ['exceptionnel', 'maladie'])) {
            return true;
        }

        if ($year === null) {
            $year = date('Y');
        }

        try {
            $balance = $this->getDepartmentLeaveBalance($departmentName, $year);
            if (!$balance) {
                return false;
            }

            return $balance['remaining_days'] >= $requestedDays;
        } catch (Exception $e) {
            error_log("Error checking department balance: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Deduct days from department leave balance
     *
     * @param string $departmentName The department name
     * @param float $days The number of days to deduct
     * @param string $leaveType The type of leave
     * @param int $year The year (defaults to current year)
     * @return bool Success or failure
     */
    public function deductDepartmentLeaveBalance($departmentName, $days, $leaveType, $year = null) {
        // Special leave types bypass the department balance deduction
        if (in_array($leaveType, ['exceptionnel', 'maladie'])) {
            return true;
        }

        if ($year === null) {
            $year = date('Y');
        }

        try {
            // Check if department has initialized balance for the year
            $balance = $this->getDepartmentLeaveBalance($departmentName, $year);
            if (!$balance) {
                return false;
            }

            // Update the balance
            $stmt = $this->db->prepare("
                UPDATE department_leave_balances
                SET
                    used_days = used_days + ?,
                    remaining_days = total_days - (used_days + ?)
                WHERE department_name = ? AND year = ?
            ");
            return $stmt->execute([$days, $days, $departmentName, $year]);
        } catch (PDOException $e) {
            error_log("Error deducting department leave balance: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Restore days to department leave balance (when a leave request is canceled or rejected)
     *
     * @param string $departmentName The department name
     * @param float $days The number of days to restore
     * @param string $leaveType The type of leave
     * @param int $year The year (defaults to current year)
     * @return bool Success or failure
     */
    public function restoreDepartmentLeaveBalance($departmentName, $days, $leaveType, $year = null) {
        // Special leave types bypass the department balance restoration
        if (in_array($leaveType, ['exceptionnel', 'maladie'])) {
            return true;
        }

        if ($year === null) {
            $year = date('Y');
        }

        try {
            // Check if department has initialized balance for the year
            $balance = $this->getDepartmentLeaveBalance($departmentName, $year);
            if (!$balance) {
                return false;
            }

            // Update the balance
            $stmt = $this->db->prepare("
                UPDATE department_leave_balances
                SET
                    used_days = GREATEST(0, used_days - ?),
                    remaining_days = total_days - GREATEST(0, used_days - ?)
                WHERE department_name = ? AND year = ?
            ");
            return $stmt->execute([$days, $days, $departmentName, $year]);
        } catch (PDOException $e) {
            error_log("Error restoring department leave balance: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all department balances
     *
     * @param int $year The year (defaults to current year)
     * @return array Array of department balances
     */
    public function getAllDepartmentBalances($year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        try {
            $stmt = $this->db->prepare("
                SELECT
                    department_name,
                    total_days,
                    used_days,
                    remaining_days
                FROM department_leave_balances
                WHERE year = ?
                ORDER BY department_name
            ");
            $stmt->execute([$year]);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error fetching all department balances: " . $e->getMessage());
            return [];
        }
    }
}
