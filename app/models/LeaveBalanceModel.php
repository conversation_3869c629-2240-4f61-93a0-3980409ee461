<?php
// Model for handling leave balance operations

class LeaveBalanceModel extends Model {

    /**
     * Initialize leave balances for a user
     *
     * @param int $userId The user ID
     * @param int $year The year (defaults to current year)
     * @return bool Success or failure
     */
    public function initializeUserLeaveBalances($userId, $year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        try {
            // Get user role to determine accrual rate
            $userModel = new UserModel();
            $user = $userModel->getUserById($userId);

            if (!$user) {
                error_log("User not found: " . $userId);
                return false;
            }

            $accrualRate = 1.4; // Default for regular employees

            if ($user['role'] === 'admin') {
                $accrualRate = 1.99;
            } elseif ($user['role'] === 'responsable') {
                $accrualRate = 1.83;
            }

            // Initialize paid leave balance
            $stmt = $this->db->prepare("
                INSERT INTO leave_balances (
                    user_id,
                    leave_type,
                    annual_allocation,
                    used_days,
                    remaining_days,
                    accrual_rate,
                    year
                ) VALUES (
                    ?,
                    'payé',
                    19,
                    0,
                    19,
                    ?,
                    ?
                ) ON DUPLICATE KEY UPDATE
                    annual_allocation = 19,
                    accrual_rate = ?
            ");

            $result1 = $stmt->execute([$userId, $accrualRate, $year, $accrualRate]);

            // Initialize other leave types with default values
            // Note: Sans solde is unlimited (set to 999)
            // Exceptional leave no longer has balance tracking - only time-based restrictions
            $stmt = $this->db->prepare("
                INSERT INTO leave_balances (
                    user_id,
                    leave_type,
                    annual_allocation,
                    used_days,
                    remaining_days,
                    accrual_rate,
                    year
                ) VALUES
                (?, 'sans solde', 999, 0, 999, 0, ?),
                (?, 'maladie', 15, 0, 15, 0, ?)
                ON DUPLICATE KEY UPDATE
                    annual_allocation = VALUES(annual_allocation),
                    remaining_days = VALUES(annual_allocation) - used_days
            ");

            $result2 = $stmt->execute([
                $userId, $year,
                $userId, $year
            ]);

            return $result1 && $result2;
        } catch (PDOException $e) {
            error_log("Error initializing user leave balances: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Apply monthly accrual for a specific user
     *
     * @param int $userId The user ID
     * @param int $year The year (defaults to current year)
     * @return bool Success or failure
     */
    public function applyMonthlyAccrualForUser($userId, $year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        try {
            // Get current date information
            $currentDate = date('Y-m-d');
            $currentMonth = date('m');

            // Check if user already has accrual for this month
            $stmt = $this->db->prepare("
                SELECT last_accrual_date, accrual_rate
                FROM leave_balances
                WHERE user_id = ? AND leave_type = 'payé' AND year = ?
            ");
            $stmt->execute([$userId, $year]);
            $balance = $stmt->fetch();

            if (!$balance) {
                // No balance record found, initialize first
                $this->initializeUserLeaveBalances($userId, $year);
                return true;
            }

            // Check if accrual is needed (no accrual this month yet)
            $lastAccrualDate = $balance['last_accrual_date'];
            $needsAccrual = false;

            if ($lastAccrualDate === null) {
                $needsAccrual = true;
            } else {
                $lastAccrualMonth = date('m', strtotime($lastAccrualDate));
                $needsAccrual = ($lastAccrualMonth < $currentMonth);
            }

            if ($needsAccrual) {
                // Start a transaction
                $this->db->beginTransaction();

                try {
                    // Add accrual history record
                    $stmt = $this->db->prepare("
                        INSERT INTO leave_accrual_history (user_id, leave_type, accrual_date, accrual_amount)
                        VALUES (?, 'payé', ?, ?)
                    ");
                    $stmt->execute([$userId, $currentDate, $balance['accrual_rate']]);

                    // Update leave balance with accrued days
                    $stmt = $this->db->prepare("
                        UPDATE leave_balances
                        SET
                            annual_allocation = annual_allocation + ?,
                            remaining_days = remaining_days + ?,
                            last_accrual_date = ?
                        WHERE user_id = ? AND leave_type = 'payé' AND year = ?
                    ");
                    $result = $stmt->execute([
                        $balance['accrual_rate'],
                        $balance['accrual_rate'],
                        $currentDate,
                        $userId,
                        $year
                    ]);

                    if ($result) {
                        $this->db->commit();
                        return true;
                    } else {
                        $this->db->rollBack();
                        return false;
                    }
                } catch (Exception $e) {
                    $this->db->rollBack();
                    error_log("Transaction error in applyMonthlyAccrualForUser: " . $e->getMessage());
                    return false;
                }
            }

            return true; // No accrual needed
        } catch (PDOException $e) {
            error_log("Error applying monthly accrual for user: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Process monthly accruals for all users
     *
     * @return bool Success or failure
     */
    public function processMonthlyAccruals() {
        try {
            // Get current date information
            $currentDate = date('Y-m-d');
            $currentYear = date('Y');
            $currentMonth = date('m');

            // Start a transaction
            $this->db->beginTransaction();

            try {
                // Process accruals for all active users
                $stmt = $this->db->prepare("
                    INSERT INTO leave_accrual_history (user_id, leave_type, accrual_date, accrual_amount)
                    SELECT
                        u.id,
                        'payé',
                        ?,
                        lb.accrual_rate
                    FROM
                        users u
                        JOIN leave_balances lb ON u.id = lb.user_id AND lb.leave_type = 'payé' AND lb.year = ?
                    WHERE
                        u.actif = 1
                        AND (lb.last_accrual_date IS NULL OR MONTH(lb.last_accrual_date) < ?)
                ");

                $result1 = $stmt->execute([$currentDate, $currentYear, $currentMonth]);

                if (!$result1) {
                    $this->db->rollBack();
                    return false;
                }

                // Update leave balances with accrued days
                $stmt = $this->db->prepare("
                    UPDATE leave_balances lb
                    JOIN users u ON lb.user_id = u.id
                    SET
                        lb.annual_allocation = lb.annual_allocation + lb.accrual_rate,
                        lb.remaining_days = lb.remaining_days + lb.accrual_rate,
                        lb.last_accrual_date = ?
                    WHERE
                        u.actif = 1
                        AND lb.leave_type = 'payé'
                        AND lb.year = ?
                        AND (lb.last_accrual_date IS NULL OR MONTH(lb.last_accrual_date) < ?)
                ");

                $result2 = $stmt->execute([$currentDate, $currentYear, $currentMonth]);

                if (!$result2) {
                    $this->db->rollBack();
                    return false;
                }

                // Commit the transaction
                $this->db->commit();
                return true;
            } catch (Exception $e) {
                $this->db->rollBack();
                error_log("Transaction error in processMonthlyAccruals: " . $e->getMessage());
                return false;
            }
        } catch (PDOException $e) {
            error_log("Error processing monthly accruals: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user's leave balances
     *
     * @param int $userId The user ID
     * @param int $year The year (defaults to current year)
     * @return array Array containing leave balances for different types
     */
    public function getUserLeaveBalances($userId, $year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        try {
            // Check if user has initialized balances for the year
            $checkStmt = $this->db->prepare("
                SELECT COUNT(*) as count
                FROM leave_balances
                WHERE user_id = ? AND year = ?
            ");
            $checkStmt->execute([$userId, $year]);
            $count = $checkStmt->fetch()['count'];

            // If no balances exist, initialize them
            if ($count == 0) {
                $this->initializeUserLeaveBalances($userId, $year);
            }

            // Get the balances
            $stmt = $this->db->prepare("
                SELECT
                    leave_type,
                    annual_allocation as total,
                    used_days as used,
                    remaining_days as remaining,
                    accrual_rate,
                    last_accrual_date
                FROM leave_balances
                WHERE user_id = ? AND year = ?
            ");
            $stmt->execute([$userId, $year]);
            $results = $stmt->fetchAll();

            // Format the results
            $balances = [];
            foreach ($results as $row) {
                $balances[$row['leave_type']] = [
                    'total' => (float)$row['total'],
                    'used' => (float)$row['used'],
                    'remaining' => (float)$row['remaining'],
                    'accrual_rate' => (float)$row['accrual_rate'],
                    'last_accrual_date' => $row['last_accrual_date']
                ];
            }

            return $balances;
        } catch (PDOException $e) {
            error_log("Error fetching user leave balances: " . $e->getMessage());
            // Return default values in case of error
            return [
                'payé' => ['total' => 19, 'used' => 0, 'remaining' => 19, 'accrual_rate' => 1.4, 'last_accrual_date' => null],
                'sans solde' => ['total' => 999, 'used' => 0, 'remaining' => 999, 'accrual_rate' => 0, 'last_accrual_date' => null],
                'maladie' => ['total' => 15, 'used' => 0, 'remaining' => 15, 'accrual_rate' => 0, 'last_accrual_date' => null]
            ];
        }
    }

    /**
     * Update leave balance when a leave request is approved
     *
     * @param int $userId The user ID
     * @param string $leaveType The leave type
     * @param float $days The number of days to deduct
     * @param int $year The year (defaults to current year)
     * @return bool Success or failure
     */
    public function deductLeaveBalance($userId, $leaveType, $days, $year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        try {
            // Check if user has initialized balances for the year
            $checkStmt = $this->db->prepare("
                SELECT COUNT(*) as count
                FROM leave_balances
                WHERE user_id = ? AND leave_type = ? AND year = ?
            ");
            $checkStmt->execute([$userId, $leaveType, $year]);
            $count = $checkStmt->fetch()['count'];

            // If no balances exist, initialize them
            if ($count == 0) {
                $this->initializeUserLeaveBalances($userId, $year);
            }

            // Update the balance
            $stmt = $this->db->prepare("
                UPDATE leave_balances
                SET
                    used_days = used_days + ?,
                    remaining_days = annual_allocation - (used_days + ?)
                WHERE user_id = ? AND leave_type = ? AND year = ?
            ");
            return $stmt->execute([$days, $days, $userId, $leaveType, $year]);
        } catch (PDOException $e) {
            error_log("Error deducting leave balance: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Restore leave balance when a leave request is cancelled or rejected
     *
     * @param int $userId The user ID
     * @param string $leaveType The leave type
     * @param float $days The number of days to restore
     * @param int $year The year (defaults to current year)
     * @return bool Success or failure
     */
    public function restoreLeaveBalance($userId, $leaveType, $days, $year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        try {
            error_log("Restoring leave balance for user $userId, type $leaveType, days $days, year $year");

            // First, get the current balance to log it
            $stmt = $this->db->prepare("
                SELECT used_days, remaining_days, annual_allocation
                FROM leave_balances
                WHERE user_id = ? AND leave_type = ? AND year = ?
            ");
            $stmt->execute([$userId, $leaveType, $year]);
            $currentBalance = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($currentBalance) {
                error_log("Current balance before restore: used_days={$currentBalance['used_days']}, remaining_days={$currentBalance['remaining_days']}, annual_allocation={$currentBalance['annual_allocation']}");
            } else {
                error_log("No current balance found for user $userId, type $leaveType, year $year");
                return false;
            }

            // Use a transaction to ensure data consistency
            $this->db->beginTransaction();

            try {
                // Update the balance in two steps to avoid calculation issues
                // Step 1: Update used_days
                $stmt = $this->db->prepare("
                    UPDATE leave_balances
                    SET used_days = GREATEST(0, used_days - ?)
                    WHERE user_id = ? AND leave_type = ? AND year = ?
                ");
                $stmt->execute([$days, $userId, $leaveType, $year]);

                // Step 2: Update remaining_days based on the new used_days
                $stmt = $this->db->prepare("
                    UPDATE leave_balances
                    SET remaining_days = annual_allocation - used_days
                    WHERE user_id = ? AND leave_type = ? AND year = ?
                ");
                $result = $stmt->execute([$userId, $leaveType, $year]);

                // Get the updated balance to log it
                $stmt = $this->db->prepare("
                    SELECT used_days, remaining_days, annual_allocation
                    FROM leave_balances
                    WHERE user_id = ? AND leave_type = ? AND year = ?
                ");
                $stmt->execute([$userId, $leaveType, $year]);
                $updatedBalance = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($updatedBalance) {
                    error_log("Updated balance after restore: used_days={$updatedBalance['used_days']}, remaining_days={$updatedBalance['remaining_days']}, annual_allocation={$updatedBalance['annual_allocation']}");
                }

                $this->db->commit();
                return $result;
            } catch (PDOException $e) {
                $this->db->rollBack();
                error_log("Error in transaction while restoring leave balance: " . $e->getMessage());
                return false;
            }
        } catch (PDOException $e) {
            error_log("Error restoring leave balance: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if user has sufficient leave balance
     *
     * @param int $userId The user ID
     * @param string $leaveType The leave type
     * @param float $requestedDays The number of days requested
     * @param int $year The year (defaults to current year)
     * @return bool True if sufficient balance, false otherwise
     */
    public function hasSufficientBalance($userId, $leaveType, $requestedDays, $year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        // Sans solde is unlimited, always return true
        if ($leaveType === 'sans solde') {
            return true;
        }

        // Exceptional leave doesn't use balance tracking - use time-based validation instead
        if ($leaveType === 'exceptionnel') {
            return true; // Balance check bypassed - validation done in controller
        }

        $balances = $this->getUserLeaveBalances($userId, $year);

        if (isset($balances[$leaveType])) {
            return $balances[$leaveType]['remaining'] >= $requestedDays;
        }

        return false;
    }

    /**
     * Check if user can request exceptional leave based on 48-hour rule
     * Users cannot request exceptional leave if they have used exceptional leave in the previous 48 hours
     *
     * @param int $userId The user ID
     * @return bool True if user can request exceptional leave, false otherwise
     */
    public function canRequestExceptionalLeave($userId) {
        try {
            // Calculate the cutoff time (48 hours ago)
            $cutoffTime = date('Y-m-d H:i:s', strtotime('-48 hours'));

            // Check for any approved exceptional leave requests in the last 48 hours
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count
                FROM demandes_conges
                WHERE user_id = ?
                AND type = 'exceptionnel'
                AND statut = 'approuvee'
                AND (
                    date_fin >= ?
                    OR (date_debut <= NOW() AND date_fin >= ?)
                )
            ");

            $stmt->execute([$userId, $cutoffTime, $cutoffTime]);
            $result = $stmt->fetch();

            return $result['count'] == 0;

        } catch (PDOException $e) {
            error_log("Error checking exceptional leave 48-hour rule: " . $e->getMessage());
            // In case of error, be conservative and deny the request
            return false;
        }
    }

    /**
     * Get the last exceptional leave end date for a user (for informational purposes)
     *
     * @param int $userId The user ID
     * @return string|null The last exceptional leave end date or null if none found
     */
    public function getLastExceptionalLeaveEndDate($userId) {
        try {
            $stmt = $this->db->prepare("
                SELECT date_fin
                FROM demandes_conges
                WHERE user_id = ?
                AND type = 'exceptionnel'
                AND statut = 'approuvee'
                ORDER BY date_fin DESC
                LIMIT 1
            ");

            $stmt->execute([$userId]);
            $result = $stmt->fetch();

            return $result ? $result['date_fin'] : null;

        } catch (PDOException $e) {
            error_log("Error getting last exceptional leave date: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get accrual history for a user
     *
     * @param int $userId The user ID
     * @param int $year The year (defaults to current year)
     * @return array Array of accrual history records
     */
    public function getAccrualHistory($userId, $year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        try {
            $stmt = $this->db->prepare("
                SELECT
                    leave_type,
                    accrual_date,
                    accrual_amount
                FROM leave_accrual_history
                WHERE user_id = ? AND YEAR(accrual_date) = ?
                ORDER BY accrual_date DESC
            ");
            $stmt->execute([$userId, $year]);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error fetching accrual history: " . $e->getMessage());
            return [];
        }
    }
}
