<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes demandes - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>

    <div class="md:ml-[240px] p-4 md:p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 class="text-xl sm:text-2xl font-bold text-gray-800">Mes demandes</h1>
                    <p class="text-sm sm:text-base text-gray-600">Historique de vos demandes de congé et d'absence</p>
                </div>

                <a href="/planificateur/nouvelle-demande" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center gap-2 text-sm sm:text-base whitespace-nowrap">
                    <i class="fas fa-plus"></i> Nouvelle demande
                </a>
            </div>
        </header>

        <div class="bg-white rounded-lg shadow-sm mb-6 p-4">
            <div class="flex flex-wrap items-center justify-between mb-4">
                <div class="w-full md:w-auto mb-4 md:mb-0">
                    <div class="relative">
                        <input type="text" id="search" placeholder="Rechercher..." value="<?= htmlspecialchars($filters['search'] ?? '') ?>"
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-auto flex flex-wrap items-center">
                    <div class="mr-2 mb-2">
                        <select id="per_page" class="pl-3 pr-10 py-2 text-sm border-gray-300 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                            <option value="5" <?= (isset($pagination['perPage']) && $pagination['perPage'] == 5) ? 'selected' : '' ?>>5 par page</option>
                            <option value="10" <?= (isset($pagination['perPage']) && $pagination['perPage'] == 10) ? 'selected' : '' ?>>10 par page</option>
                            <option value="25" <?= (isset($pagination['perPage']) && $pagination['perPage'] == 25) ? 'selected' : '' ?>>25 par page</option>
                            <option value="50" <?= (isset($pagination['perPage']) && $pagination['perPage'] == 50) ? 'selected' : '' ?>>50 par page</option>
                        </select>
                    </div>
                    <button id="toggleFilters" class="bg-gray-100 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-200 transition flex items-center gap-1 text-sm mb-2">
                        <i class="fas fa-sliders-h"></i> Filtres
                    </button>
                </div>
            </div>

            <div id="filterPanel" class="border-t pt-4 mt-2 <?= (!empty($filters['period']) && $filters['period'] !== 'all') ||
                                                              (!empty($filters['status']) && $filters['status'] !== 'all') ||
                                                              (!empty($filters['type']) && $filters['type'] !== 'all') ? '' : 'hidden' ?>">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="period" class="block text-sm font-medium text-gray-700 mb-1">Période</label>
                        <select id="period" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option value="all" <?= (isset($filters['period']) && $filters['period'] === 'all') ? 'selected' : '' ?>>Toutes les périodes</option>
                            <option value="30" <?= (isset($filters['period']) && $filters['period'] === '30') ? 'selected' : '' ?>>30 derniers jours</option>
                            <option value="90" <?= (isset($filters['period']) && $filters['period'] === '90') ? 'selected' : '' ?>>90 derniers jours</option>
                            <option value="180" <?= (isset($filters['period']) && $filters['period'] === '180') ? 'selected' : '' ?>>6 derniers mois</option>
                            <option value="365" <?= (isset($filters['period']) && $filters['period'] === '365') ? 'selected' : '' ?>>12 derniers mois</option>
                        </select>
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                        <select id="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option value="all" <?= (isset($filters['status']) && $filters['status'] === 'all') ? 'selected' : '' ?>>Tous les statuts</option>
                            <option value="pending" <?= (isset($filters['status']) && $filters['status'] === 'pending') ? 'selected' : '' ?>>En attente</option>
                            <option value="approved" <?= (isset($filters['status']) && $filters['status'] === 'approved') ? 'selected' : '' ?>>Approuvée</option>
                            <option value="rejected" <?= (isset($filters['status']) && $filters['status'] === 'rejected') ? 'selected' : '' ?>>Rejetée</option>
                        </select>
                    </div>
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type de congé</label>
                        <select id="type" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option value="all" <?= (isset($filters['type']) && $filters['type'] === 'all') ? 'selected' : '' ?>>Tous les types</option>
                            <option value="cp" <?= (isset($filters['type']) && $filters['type'] === 'cp') ? 'selected' : '' ?>>Congé payé</option>
                            <option value="css" <?= (isset($filters['type']) && $filters['type'] === 'css') ? 'selected' : '' ?>>Congé sans solde</option>
                            <option value="cm" <?= (isset($filters['type']) && $filters['type'] === 'cm') ? 'selected' : '' ?>>Congé maladie</option>
                            <option value="cf" <?= (isset($filters['type']) && $filters['type'] === 'cf') ? 'selected' : '' ?>>Congé familial</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4 flex justify-end">
                    <button id="filterButton" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition flex items-center gap-2">
                        <i class="fas fa-filter"></i> Appliquer les filtres
                    </button>
                    <a href="/planificateur/mes-demandes" class="ml-2 text-gray-600 hover:text-gray-800 underline text-sm flex items-center">
                        <i class="fas fa-times mr-1"></i> Réinitialiser
                    </a>
                </div>
            </div>
        </div>

        <?php if (empty($demandes)): ?>
            <div class="card">
                <p class="text-gray-500 text-center py-8">Vous n'avez pas encore fait de demandes de congé ou d'absence.</p>
                <div class="text-center">
                    <a href="/planificateur/nouvelle-demande" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition inline-block">
                        Créer votre première demande
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="table-card">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Référence
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <a href="#" class="sort-header flex items-center" data-sort="type">
                                        Type
                                        <?php if (isset($sorting['sortBy']) && $sorting['sortBy'] === 'type'): ?>
                                            <i class="fas fa-sort-<?= $sorting['sortOrder'] === 'ASC' ? 'up' : 'down' ?> ml-1"></i>
                                        <?php else: ?>
                                            <i class="fas fa-sort ml-1 text-gray-400"></i>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <a href="#" class="sort-header flex items-center" data-sort="date_debut">
                                        Date Début
                                        <?php if (isset($sorting['sortBy']) && $sorting['sortBy'] === 'date_debut'): ?>
                                            <i class="fas fa-sort-<?= $sorting['sortOrder'] === 'ASC' ? 'up' : 'down' ?> ml-1"></i>
                                        <?php else: ?>
                                            <i class="fas fa-sort ml-1 text-gray-400"></i>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <a href="#" class="sort-header flex items-center" data-sort="date_fin">
                                        Date Fin
                                        <?php if (isset($sorting['sortBy']) && $sorting['sortBy'] === 'date_fin'): ?>
                                            <i class="fas fa-sort-<?= $sorting['sortOrder'] === 'ASC' ? 'up' : 'down' ?> ml-1"></i>
                                        <?php else: ?>
                                            <i class="fas fa-sort ml-1 text-gray-400"></i>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <a href="#" class="sort-header flex items-center" data-sort="statut">
                                        Statut
                                        <?php if (isset($sorting['sortBy']) && $sorting['sortBy'] === 'statut'): ?>
                                            <i class="fas fa-sort-<?= $sorting['sortOrder'] === 'ASC' ? 'up' : 'down' ?> ml-1"></i>
                                        <?php else: ?>
                                            <i class="fas fa-sort ml-1 text-gray-400"></i>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validé par</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($demandes as $demande): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?= !empty($demande['reference_demande']) ? htmlspecialchars($demande['reference_demande']) : '<span class="text-gray-400">-</span>' ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                        <?php
                                            $demandeModel = new DemandeModel();
                                            echo htmlspecialchars($demandeModel->formatLeaveType($demande['type']));
                                        ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= date('d/m/Y', strtotime($demande['date_debut'])) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= date('d/m/Y', strtotime($demande['date_fin'])) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?php
                                                $start = new DateTime($demande['date_debut']);
                                                $end = new DateTime($demande['date_fin']);
                                                $interval = $start->diff($end);
                                                $days = $interval->days + 1;

                                                // Adjust for half-days if applicable
                                                if (isset($demande['demi_journee']) && $demande['demi_journee']) {
                                                    $demiJours = 0;
                                                    if (strpos($demande['demi_type'] ?? '', 'matin') !== false) {
                                                        $demiJours += 0.5;
                                                    }
                                                    if (strpos($demande['demi_type'] ?? '', 'apres-midi') !== false) {
                                                        $demiJours += 0.5;
                                                    }

                                                    if ($demiJours > 0) {
                                                        $days -= $demiJours;
                                                    }
                                                }

                                                echo $days . ' jour(s)';
                                            ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $statut = strtolower($demande['statut']);
                                        if ($statut === 'en cours'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                En attente
                                            </span>
                                        <?php elseif ($statut === 'acceptée'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Approuvée
                                            </span>
                                        <?php elseif ($statut === 'refusee'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Rejetée
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                <?= htmlspecialchars(ucfirst($demande['statut'])) ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?php if (strtolower($demande['statut']) === 'acceptée' || strtolower($demande['statut']) === 'refusee'): ?>
                                                <?php if (isset($demande['valideur_nom']) && isset($demande['valideur_prenom'])): ?>
                                                    <?= htmlspecialchars($demande['valideur_prenom'] . ' ' . $demande['valideur_nom']) ?>
                                                <?php else: ?>
                                                    Non renseigné
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-gray-400">-</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div class="flex items-center space-x-2">
                                            <!-- View details action - always available -->
                                            <a href="/planificateur/details-demande?id=<?= $demande['id'] ?>" class="text-indigo-600 hover:text-indigo-900 mr-1">
                                                <i class="fas fa-eye"></i> Voir détails
                                            </a>

                                            <!-- Cancel action - only for pending requests -->
                                            <?php if (strtolower($demande['statut']) === 'en cours'): ?>
                                                <a href="#" onclick="handleCancellation(event, '/planificateur/annuler-demande?id=<?= $demande['id'] ?>')" class="text-red-600 hover:text-red-900">
                                                    <i class="fas fa-times"></i> Annuler
                                                </a>
                                            <?php endif; ?>

                                            <!-- Justificatif link - if available -->
                                            <?php if (!empty($demande['justificatif'])): ?>
                                                <a href="/uploads/justificatifs/<?= htmlspecialchars($demande['justificatif']) ?>" target="_blank" class="text-purple-600 hover:text-purple-800">
                                                    <i class="fas fa-file-download"></i> Justificatif
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Filter elements
            const filterButton = document.getElementById('filterButton');
            const toggleFiltersButton = document.getElementById('toggleFilters');
            const filterPanel = document.getElementById('filterPanel');
            const periodFilter = document.getElementById('period');
            const statusFilter = document.getElementById('status');
            const typeFilter = document.getElementById('type');
            const searchInput = document.getElementById('search');
            const perPageSelect = document.getElementById('per_page');
            const sortHeaders = document.querySelectorAll('.sort-header');

            // Toggle filter panel
            toggleFiltersButton.addEventListener('click', function() {
                filterPanel.classList.toggle('hidden');
            });

            // Add event listener to filter button
            filterButton.addEventListener('click', applyFilters);

            // Add event listener to per page select
            perPageSelect.addEventListener('change', function() {
                applyFilters();
            });

            // Add event listeners to sort headers
            sortHeaders.forEach(header => {
                header.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sortBy = this.getAttribute('data-sort');
                    let sortOrder = 'ASC';

                    // If already sorting by this column, toggle the order
                    if (getUrlParameter('sort_by') === sortBy) {
                        sortOrder = getUrlParameter('sort_order') === 'ASC' ? 'DESC' : 'ASC';
                    }

                    // Apply sorting
                    const urlParams = new URLSearchParams(window.location.search);
                    urlParams.set('sort_by', sortBy);
                    urlParams.set('sort_order', sortOrder);

                    // Redirect to sorted URL
                    window.location.href = window.location.pathname + '?' + urlParams.toString();
                });
            });

            // Add event listener for search input (with debounce)
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    applyFilters();
                }, 500);
            });

            function applyFilters() {
                // Get filter values
                const period = periodFilter.value;
                const status = statusFilter.value;
                const type = typeFilter.value;
                const search = searchInput.value.trim();
                const perPage = perPageSelect.value;

                // Get current sort parameters
                const sortBy = getUrlParameter('sort_by') || 'date_demande';
                const sortOrder = getUrlParameter('sort_order') || 'DESC';

                // Create URL with query parameters
                let url = window.location.pathname + '?';
                let params = [];

                if (period !== 'all') params.push('period=' + period);
                if (status !== 'all') params.push('status=' + status);
                if (type !== 'all') params.push('type=' + type);
                if (search !== '') params.push('search=' + encodeURIComponent(search));
                if (perPage !== '5') params.push('per_page=' + perPage);
                if (sortBy !== 'date_demande') params.push('sort_by=' + sortBy);
                if (sortOrder !== 'DESC') params.push('sort_order=' + sortOrder);

                url += params.join('&');

                // Redirect to filtered URL
                window.location.href = params.length > 0 ? url : window.location.pathname;
            }

            // Helper function to get URL parameters
            function getUrlParameter(name) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name);
            }

            // Preselect filters based on URL parameters
            function preselectFilters() {
                const urlParams = new URLSearchParams(window.location.search);

                if (urlParams.has('period')) {
                    periodFilter.value = urlParams.get('period');
                }

                if (urlParams.has('status')) {
                    statusFilter.value = urlParams.get('status');
                }

                if (urlParams.has('type')) {
                    typeFilter.value = urlParams.get('type');
                }

                if (urlParams.has('search')) {
                    searchInput.value = urlParams.get('search');
                }

                if (urlParams.has('per_page')) {
                    perPageSelect.value = urlParams.get('per_page');
                }
            }

            // Initialize preselection
            preselectFilters();
        });

        // Modern confirmation for request cancellation
        async function handleCancellation(event, url) {
            event.preventDefault();

            try {
                const confirmed = await confirmWarning(
                    'Êtes-vous sûr de vouloir annuler cette demande de congé ?<br><br>' +
                    '<div class="bg-yellow-50 p-3 rounded-lg mt-3">' +
                    '<p class="text-sm text-yellow-800 font-medium">⚠️ Cette action est irréversible</p>' +
                    '<p class="text-sm text-yellow-700">Une fois annulée, vous devrez créer une nouvelle demande si nécessaire.</p>' +
                    '</div>',
                    'Annuler la demande'
                );

                if (confirmed) {
                    window.location.href = url;
                }
            } catch (error) {
                console.error('Error in cancellation confirmation:', error);
            }
        }
    </script>
</body>
</html>
