<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Historique des attributions - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Historique des attributions de congés</h1>
                    <p class="text-gray-600">Détail des attributions de congés pour l'utilisateur</p>
                </div>
                <a href="/admin/leave-balances" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Retour
                </a>
            </div>
        </header>

        <div class="card mb-6">
            <h2 class="text-lg font-semibold mb-4">Informations de l'employé</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-gray-600">Nom:</p>
                    <p class="font-medium"><?= $user['prenom'] ?> <?= $user['nom'] ?></p>
                </div>
                <div>
                    <p class="text-gray-600">Email:</p>
                    <p class="font-medium"><?= $user['email'] ?></p>
                </div>
                <div>
                    <p class="text-gray-600">Département:</p>
                    <p class="font-medium"><?= $user['departement'] ?></p>
                </div>
                <div>
                    <p class="text-gray-600">Rôle:</p>
                    <p class="font-medium"><?= ucfirst($user['role']) ?></p>
                </div>
            </div>
        </div>

        <div class="card">
            <h2 class="text-lg font-semibold mb-4">Historique des attributions</h2>

            <?php if (empty($history)): ?>
                <p class="text-gray-500">Aucune attribution de congés enregistrée pour cet employé.</p>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Type de congé
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Jours attribués
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($history as $entry): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= date('d/m/Y', strtotime($entry['accrual_date'])) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?php
                                            switch ($entry['leave_type']) {
                                                case 'payé':
                                                    echo 'Congés payés';
                                                    break;
                                                case 'exceptionnel':
                                                    echo 'Congés exceptionnels';
                                                    break;
                                                case 'sans solde':
                                                    echo 'Congés sans solde';
                                                    break;
                                                case 'maladie':
                                                    echo 'Congés maladie';
                                                    break;

                                                default:
                                                    echo $entry['leave_type'];
                                            }
                                            ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= $entry['accrual_amount'] ?> jour(s)
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
    </script>
</body>
</html>
