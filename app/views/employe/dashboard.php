<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Employé</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>

<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_employe.php'; ?>

    <div class="md:ml-[240px] p-4 md:p-6 transition-all duration-200" id="main-content">
        <div>
            <h1 class="text-4xl font-bold text-gray-800">Tableau de bord</h1>
            <p class="text-3xl text-gray-600">Bienvenue,
                <?= htmlspecialchars($_SESSION['prenom'] . ' ' . $_SESSION['nom']) ?> 👋 </p>
        </div>
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="/notifications" class="relative">
                        <i class="fas fa-bell text-gray-600 text-xl"></i>
                        <?php if (count($notificationsImportantes) > 0): ?>
                        <span
                            class="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full"><?= count($notificationsImportantes) ?></span>
                        <?php endif; ?>
                    </a>
                    <a href="/profil" class="flex items-center">
                        <div
                            class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-800 font-bold">
                            <?= strtoupper(substr($_SESSION['prenom'], 0, 1)) ?>
                        </div>
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            <!-- Carte des congés disponibles -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Mes congés</h2>
                    <span class="text-purple-600"><i class="fas fa-calendar-check"></i></span>
                </div>
                <div class="space-y-3 mb-4">
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-gray-600">Congés payés :</span>
                            <span class="text-lg font-bold text-gray-900"><?= $solde_paye ?> jours</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <?php
                            $solde_paye_used = isset($solde_paye_used) ? $solde_paye_used : 0;
                            $solde_paye_total = isset($solde_paye_total) ? $solde_paye_total : 0;
                            $payePercentage = ($solde_paye_total > 0) ? ($solde_paye_used / $solde_paye_total) * 100 : 0;
                            $payePercentage = min(100, max(0, $payePercentage));
                            ?>
                            <div class="bg-green-500 h-2 rounded-full" style="width: <?= 100 - $payePercentage ?>%">
                            </div>
                        </div>
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Utilisé: <?= $solde_paye_used ?> jours</span>
                            <span>Total: <?= $solde_paye_total ?> jours</span>
                        </div>
                    </div>

                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-gray-600">Congés exceptionnels :</span>
                            <span class="text-lg font-bold text-gray-900">
                                <?php if (isset($can_request_exceptional) && $can_request_exceptional): ?>
                                    <span class="text-green-600">Disponible</span>
                                <?php else: ?>
                                    <span class="text-orange-600">Indisponible</span>
                                <?php endif; ?>
                            </span>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">
                            <span>Max 3 jours par demande</span>
                            <?php if (isset($last_exceptional_date) && $last_exceptional_date): ?>
                                <br><span>Dernier utilisé: <?= date('d/m/Y', strtotime($last_exceptional_date)) ?></span>
                            <?php endif; ?>
                        </div>
                        <?php if (!isset($can_request_exceptional) || !$can_request_exceptional): ?>
                        <div class="text-xs text-orange-600 mt-1">
                            <span>Règle 48h: Attendez 48h après votre dernier congé exceptionnel</span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-gray-600">Congés sans solde :</span>
                            <span class="text-lg font-bold text-gray-900">Illimité</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Utilisé: <?= $solde_sans_solde_used ?> jours</span>
                            <span>Total: Illimité</span>
                        </div>
                    </div>
                </div>

                <div class="flex justify-between text-sm">
                    <div>
                        <p class="text-gray-500">Dernière demande:</p>
                        <p class="font-medium"><?= htmlspecialchars($derniereDemandeType) ?> -
                            <?= htmlspecialchars($derniereDemandeDate) ?></p>
                    </div>
                    <a href="/nouvelle-demande" class="text-purple-600 font-medium hover:text-purple-800">Demander <i
                            class="fas fa-arrow-right ml-1"></i></a>
                </div>

                <?php if (isset($next_accrual_date)): ?>
                <div class="mt-3 pt-3 border-t border-gray-100 text-xs text-gray-500">
                    <p>Prochaine attribution: <span
                            class="font-medium"><?= date('d/m/Y', strtotime($next_accrual_date)) ?></span></p>
                    <p>Taux mensuel: <span class="font-medium"><?= $accrual_rate ?> jour(s)</span></p>
                </div>
                <?php endif; ?>
            </div>

            <!-- Carte des demandes récentes -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Demandes récentes</h2>
                    <span class="text-purple-600"><i class="fas fa-list"></i></span>
                </div>
                <?php if (count($demandesRecentes) > 0): ?>
                <ul class="space-y-3">
                    <?php foreach ($demandesRecentes as $demande): ?>
                    <li class="flex justify-between items-center text-sm">
                        <span><?= htmlspecialchars($demande['type']) ?> -
                            <?= htmlspecialchars($demande['date']) ?></span>
                        <?php if ($demande['statut'] === 'En attente'): ?>
                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">En attente</span>
                        <?php elseif ($demande['statut'] === 'Approuvée'): ?>
                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">Approuvée</span>
                        <?php else: ?>
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">Traitée</span>
                        <?php endif; ?>
                    </li>
                    <?php endforeach; ?>
                </ul>
                <a href="/mes-demandes"
                    class="mt-4 block text-purple-600 text-sm font-medium hover:text-purple-800">Voir toutes mes
                    demandes <i class="fas fa-arrow-right ml-1"></i></a>
                <?php else: ?>
                <p class="text-gray-500">Aucune demande récente</p>
                <?php endif; ?>
            </div>

            <!-- Carte des prochaines absences -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Absences à Venir</h2>
                    <span class="text-purple-600"><i class="fas fa-calendar-minus"></i></span>
                </div>
                <?php if (count($absencesAVenir) > 0): ?>
                <ul class="space-y-3">
                    <?php foreach ($absencesAVenir as $absence): ?>
                    <li class="text-sm">
                        <div class="flex justify-between">
                            <?php if ($absence['date_debut'] === $absence['date_fin']): ?>
                            <span class="font-medium"><?= date('d/m/Y', strtotime($absence['date_debut'])) ?></span>
                            <?php else: ?>
                            <span class="font-medium"><?= date('d/m/Y', strtotime($absence['date_debut'])) ?> -
                                <?= date('d/m/Y', strtotime($absence['date_fin'])) ?></span>
                            <?php endif; ?>
                        </div>
                        <p class="text-gray-500"><?= htmlspecialchars($absence['motif']) ?></p>
                    </li>
                    <?php endforeach; ?>
                </ul>
                <?php else: ?>
                <p class="text-gray-500">Aucune absence prévue</p>
                <?php endif; ?>
            </div>

            <!-- Carte des notifications -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Notifications importantes</h2>
                    <span class="text-purple-600"><i class="fas fa-bell"></i></span>
                </div>
                <?php if (count($notificationsImportantes) > 0): ?>
                <ul class="space-y-3">
                    <?php foreach ($notificationsImportantes as $notification): ?>
                    <li class="text-sm bg-purple-50 p-3 rounded">
                        <?= htmlspecialchars($notification['message']) ?>
                    </li>
                    <?php endforeach; ?>
                </ul>
                <a href="/notifications"
                    class="mt-4 block text-purple-600 text-sm font-medium hover:text-purple-800">Voir toutes les
                    notifications <i class="fas fa-arrow-right ml-1"></i></a>
                <?php else: ?>
                <p class="text-gray-500">Aucune notification importante</p>
                <?php endif; ?>
            </div>

            <!-- Carte des statistiques -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Mes statistiques</h2>
                    <span class="text-purple-600"><i class="fas fa-chart-pie"></i></span>
                </div>
                <div class="space-y-3">
                    <div>
                        <p class="text-sm text-gray-500">Demandes ce mois</p>
                        <p class="text-2xl font-bold">4</p>
                        <!-- <p class="text-2xl font-bold"><?= $demandesSoumisesCeMois ?></p> -->
                    </div>
                    <div>
                        <div class="flex justify-between mb-1">
                            <p class="text-sm text-gray-500">Taux d'approbation</p>
                            <span class="text-sm font-medium"><?= $pourcentageDemandesApprouvees ?>%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-purple-600 h-2.5 rounded-full"
                                style="width: <?= $pourcentageDemandesApprouvees ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carte des jours fériés -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Jours Fériés</h2>
                    <span class="text-purple-600"><i class="fas fa-calendar-alt"></i></span>
                </div>

                <div class="flex space-x-2 mb-4">
                    <button onclick="showHolidays('france')"
                        class="px-3 py-1 text-sm font-medium rounded-md bg-purple-100 text-purple-700 hover:bg-purple-200 focus:outline-none"
                        id="btn-france">France</button>
                    <button onclick="showHolidays('tunisie')"
                        class="px-3 py-1 text-sm font-medium rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none"
                        id="btn-tunisie">Tunisie</button>
                </div>

                <?php
                // Inclure le fichier des jours fériés
                require_once APP_PATH . '/data/jours_feries.php';

                // Obtenir les prochains jours fériés
                $prochainsFrance = getProchainsJoursFeries($joursFeriesFrancais, 2);
                $prochainsTunisie = getProchainsJoursFeries($joursFeriesTunisie, 2);
                ?>

                <div id="holidays-france" class="space-y-3">
                    <?php if (count($prochainsFrance) > 0): ?>
                    <?php foreach ($prochainsFrance as $date => $nom): ?>
                    <div class="text-sm">
                        <p class="font-medium"><?= htmlspecialchars($nom) ?></p>
                        <p class="text-gray-500"><?= date('d/m/Y', strtotime($date)) ?></p>
                    </div>
                    <?php endforeach; ?>
                    <a href="#" onclick="openHolidaysModal('france'); return false;"
                        class="text-purple-600 text-sm font-medium hover:text-purple-800">
                        Voir tous les jours fériés <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                    <?php else: ?>
                    <p class="text-gray-500">Aucun jour férié à venir</p>
                    <?php endif; ?>
                </div>

                <div id="holidays-tunisie" class="space-y-3 hidden">
                    <?php if (count($prochainsTunisie) > 0): ?>
                    <?php foreach ($prochainsTunisie as $date => $nom): ?>
                    <div class="text-sm">
                        <p class="font-medium"><?= htmlspecialchars($nom) ?></p>
                        <p class="text-gray-500"><?= date('d/m/Y', strtotime($date)) ?></p>
                    </div>
                    <?php endforeach; ?>
                    <a href="#" onclick="openHolidaysModal('tunisie'); return false;"
                        class="text-purple-600 text-sm font-medium hover:text-purple-800">
                        Voir tous les jours fériés <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                    <?php else: ?>
                    <p class="text-gray-500">Aucun jour férié à venir</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour les jours fériés -->
    <div id="holidaysModal"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden flex items-center justify-center p-4 z-50">
        <div class="modal-card max-w-md w-full">
            <div class="bg-purple-600 px-4 py-3 flex items-center justify-between">
                <h3 class="text-lg font-medium text-white" id="modalTitle">Jours Fériés</h3>
                <button type="button" class="text-white hover:text-gray-200" onclick="closeHolidaysModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="flex space-x-2 mb-4">
                    <button onclick="showModalHolidays('france')"
                        class="px-3 py-1 text-sm font-medium rounded-md bg-purple-100 text-purple-700 hover:bg-purple-200 focus:outline-none"
                        id="modal-btn-france">France</button>
                    <button onclick="showModalHolidays('tunisie')"
                        class="px-3 py-1 text-sm font-medium rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none"
                        id="modal-btn-tunisie">Tunisie</button>
                </div>

                <div id="modal-holidays-france" class="space-y-3">
                    <?php foreach ($joursFeriesFrancais as $date => $nom): ?>
                    <div class="text-sm py-2 border-b border-gray-100">
                        <p class="font-medium"><?= htmlspecialchars($nom) ?></p>
                        <p class="text-gray-500"><?= date('d/m/Y', strtotime($date)) ?></p>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div id="modal-holidays-tunisie" class="space-y-3 hidden">
                    <?php foreach ($joursFeriesTunisie as $date => $nom): ?>
                    <div class="text-sm py-2 border-b border-gray-100">
                        <p class="font-medium"><?= htmlspecialchars($nom) ?></p>
                        <p class="text-gray-500"><?= date('d/m/Y', strtotime($date)) ?></p>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
    function showHolidays(country) {
        // Hide all holiday lists
        document.getElementById('holidays-france').classList.add('hidden');
        document.getElementById('holidays-tunisie').classList.add('hidden');

        // Reset all buttons to inactive state
        document.getElementById('btn-france').classList.remove('bg-purple-100', 'text-purple-700');
        document.getElementById('btn-france').classList.add('bg-gray-100', 'text-gray-700');
        document.getElementById('btn-tunisie').classList.remove('bg-purple-100', 'text-purple-700');
        document.getElementById('btn-tunisie').classList.add('bg-gray-100', 'text-gray-700');

        // Show selected country and activate its button
        document.getElementById('holidays-' + country).classList.remove('hidden');
        document.getElementById('btn-' + country).classList.remove('bg-gray-100', 'text-gray-700');
        document.getElementById('btn-' + country).classList.add('bg-purple-100', 'text-purple-700');
    }

    function openHolidaysModal(country) {
        // Show the modal
        document.getElementById('holidaysModal').classList.remove('hidden');

        // Show the selected country's holidays
        showModalHolidays(country);
    }

    function closeHolidaysModal() {
        document.getElementById('holidaysModal').classList.add('hidden');
    }

    function showModalHolidays(country) {
        // Hide all holiday lists
        document.getElementById('modal-holidays-france').classList.add('hidden');
        document.getElementById('modal-holidays-tunisie').classList.add('hidden');

        // Reset all buttons to inactive state
        document.getElementById('modal-btn-france').classList.remove('bg-purple-100', 'text-purple-700');
        document.getElementById('modal-btn-france').classList.add('bg-gray-100', 'text-gray-700');
        document.getElementById('modal-btn-tunisie').classList.remove('bg-purple-100', 'text-purple-700');
        document.getElementById('modal-btn-tunisie').classList.add('bg-gray-100', 'text-gray-700');

        // Show selected country and activate its button
        document.getElementById('modal-holidays-' + country).classList.remove('hidden');
        document.getElementById('modal-btn-' + country).classList.remove('bg-gray-100', 'text-gray-700');
        document.getElementById('modal-btn-' + country).classList.add('bg-purple-100', 'text-purple-700');
    }
    </script>
</body>

</html>