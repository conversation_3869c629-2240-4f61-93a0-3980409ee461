<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Membres de l'équipe - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_responsable.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Membres de l'équipe</h1>
                    <p class="text-gray-600"><PERSON><PERSON><PERSON> les membres de votre équipe et leurs congés</p>
                </div>
                <div class="flex space-x-2">
                    <a href="/responsable/team_availability" class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-calendar-alt mr-2"></i> Voir le calendrier de disponibilité
                    </a>
                </div>
            </div>
        </header>

        <!-- Team Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <a href="/responsable/team_members" class="card hover:shadow-lg transition-shadow duration-200 cursor-pointer">
                <div class="flex items-center">
                    <div class="rounded-full bg-indigo-100 p-3 mr-4">
                        <i class="fas fa-users text-indigo-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Membres de l'équipe</p>
                        <p class="text-2xl font-semibold"><?= count($teamMembers) ?></p>
                    </div>
                </div>
            </a>
            <a href="#" onclick="filterAvailableMembers()" class="card hover:shadow-lg transition-shadow duration-200 cursor-pointer">
                <div class="flex items-center">
                    <div class="rounded-full bg-green-100 p-3 mr-4">
                        <i class="fas fa-user-check text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Membres disponibles</p>
                        <p class="text-2xl font-semibold"><?= count($teamMembers) - $membersOnLeave ?></p>
                    </div>
                </div>
            </a>
            <a href="#" onclick="filterAbsentMembers()" class="card hover:shadow-lg transition-shadow duration-200 cursor-pointer">
                <div class="flex items-center">
                    <div class="rounded-full bg-red-100 p-3 mr-4">
                        <i class="fas fa-user-clock text-red-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Membres absents</p>
                        <p class="text-2xl font-semibold"><?= $membersOnLeave ?></p>
                    </div>
                </div>
            </a>
            <a href="/responsable/demandes_approbation" class="card hover:shadow-lg transition-shadow duration-200 cursor-pointer">
                <div class="flex items-center">
                    <div class="rounded-full bg-yellow-100 p-3 mr-4">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Demandes en attente</p>
                        <p class="text-2xl font-semibold"><?= $membersWithPendingRequests ?></p>
                    </div>
                </div>
            </a>
        </div>

        <!-- Team Members List -->
        <div class="card">
            <div class="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <h2 class="text-lg font-semibold text-gray-800">Liste des membres</h2>

                <!-- Filters -->
                <div class="flex flex-wrap gap-3">
                    <!-- Department Filter -->
                    <div>
                        <select id="departmentFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5"
                                onchange="window.location.href = '/responsable/team_members?department=' + this.value + '&status=<?= $statusFilter ?>'">
                            <option value="all" <?= $departmentFilter == 'all' ? 'selected' : '' ?>>Tous les départements</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?= htmlspecialchars($dept) ?>" <?= $departmentFilter == $dept ? 'selected' : '' ?>><?= htmlspecialchars($dept) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <select id="statusFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5"
                                onchange="window.location.href = '/responsable/team_members?department=<?= $departmentFilter ?>&status=' + this.value">
                            <option value="all" <?= $statusFilter == 'all' ? 'selected' : '' ?>>Tous les statuts</option>
                            <option value="active" <?= $statusFilter == 'active' ? 'selected' : '' ?>>Actifs</option>
                            <option value="inactive" <?= $statusFilter == 'inactive' ? 'selected' : '' ?>>Inactifs</option>
                        </select>
                    </div>

                    <!-- Search -->
                    <div class="relative">
                        <input type="text" id="searchMember" placeholder="Rechercher un membre..." class="pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>

                    <!-- Clear Filter Button (hidden by default) -->
                    <button id="clearFilterBtn" onclick="clearFilter()" class="hidden bg-gray-100 hover:bg-gray-200 text-gray-700 py-2.5 px-4 rounded-lg flex items-center">
                        <i class="fas fa-times mr-2"></i> Effacer le filtre
                    </button>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employé</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Département</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'entrée</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Solde Congé payé</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jours pris</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="teamMembersTable">
                        <?php if (empty($teamMembers)): ?>
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                    Aucun membre ne correspond aux critères de recherche
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($teamMembers as $member): ?>
                                <tr class="member-row hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                                <span class="text-indigo-800 font-medium"><?= strtoupper(substr($member['prenom'], 0, 1) . substr($member['nom'], 0, 1)) ?></span>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900 member-name"><?= htmlspecialchars($member['prenom'] . ' ' . $member['nom']) ?></div>
                                                <div class="text-sm text-gray-500"><?= htmlspecialchars($member['email']) ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= htmlspecialchars($member['departement'] ?: 'Non défini') ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= date('d/m/Y', strtotime($member['date_entree'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="text-sm text-gray-900 mr-2"><?= $member['joursRestants'] ?></span>
                                            <div class="w-24 bg-gray-200 rounded-full h-2.5">
                                                <?php
                                                    // Calculate percentage based on base paid leave allocation (19 days)
                                                    $basePaidLeave = 19; // Base allocation without monthly accruals
                                                    $percentage = $basePaidLeave > 0 ? min(100, ($member['joursRestants'] / $basePaidLeave) * 100) : 0;

                                                    // Color coding based on remaining days
                                                    $colorClass = $member['joursRestants'] > ($basePaidLeave * 0.5) ? 'bg-green-600' :
                                                                 ($member['joursRestants'] > ($basePaidLeave * 0.25) ? 'bg-yellow-500' : 'bg-red-500');
                                                ?>
                                                <div class="<?= $colorClass ?> h-2.5 rounded-full" style="width: <?= $percentage ?>%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= $member['joursPris'] ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($member['is_absent_today']): ?>
                                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Absent
                                            </span>
                                        <?php elseif (!empty($member['prochaineAbsence']) && strtotime($member['prochaineAbsence']) <= strtotime('+7 days')): ?>
                                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Congé prochain
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Disponible
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-3">
                                            <a href="#" class="text-indigo-600 hover:text-indigo-900" onclick="showMemberDetails(<?= $member['id'] ?>)">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/responsable/historique_demandes?employee=<?= $member['id'] ?>" class="text-indigo-600 hover:text-indigo-900">
                                                <i class="fas fa-history"></i>
                                            </a>
                                            <?php if ($member['pending_requests'] > 0): ?>
                                                <a href="/responsable/demandes_approbation?employee=<?= $member['id'] ?>" class="text-yellow-500 hover:text-yellow-700" title="<?= $member['pending_requests'] ?> demande(s) en attente">
                                                    <i class="fas fa-clock"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Member Details Modal -->
    <div id="memberDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Détails de l'employé</h3>
                <button onclick="closeMemberModal()" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="memberDetailsContent" class="space-y-6">
                <!-- Content will be loaded dynamically -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
                    <p class="mt-2 text-gray-600">Chargement...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check for filter parameter in URL
            const urlParams = new URLSearchParams(window.location.search);
            const filter = urlParams.get('filter');

            if (filter === 'available') {
                filterAvailableMembers();
            } else if (filter === 'absent') {
                filterAbsentMembers();
            }

            // Search functionality
            const searchInput = document.getElementById('searchMember');
            searchInput.addEventListener('keyup', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('#teamMembersTable .member-row');

                rows.forEach(row => {
                    const name = row.querySelector('.member-name').textContent.toLowerCase();
                    const email = row.querySelector('.text-gray-500').textContent.toLowerCase();
                    const department = row.cells[1].textContent.toLowerCase();

                    if (name.includes(searchTerm) || email.includes(searchTerm) || department.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });

        function showMemberDetails(memberId) {
            const modal = document.getElementById('memberDetailsModal');
            const contentDiv = document.getElementById('memberDetailsContent');

            // Show loading state
            contentDiv.innerHTML = `
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
                    <p class="mt-2 text-gray-600">Chargement...</p>
                </div>
            `;

            modal.classList.remove('hidden');

            // Find the member in the array
            <?php
            echo "const members = " . json_encode($teamMembers) . ";";
            echo "const memberLeaveStats = " . json_encode($memberLeaveStats) . ";";
            ?>

            const member = members.find(m => m.id == memberId);
            const stats = memberLeaveStats[memberId] || {};

            if (member) {
                contentDiv.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="flex items-center mb-6">
                                <div class="h-16 w-16 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                                    <span class="text-indigo-800 text-xl font-medium">${member.prenom.charAt(0)}${member.nom.charAt(0)}</span>
                                </div>
                                <div>
                                    <h4 class="text-xl font-medium">${member.prenom} ${member.nom}</h4>
                                    <p class="text-gray-500">${member.email}</p>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Poste</p>
                                        <p class="text-sm text-gray-900">${member.poste || 'Non défini'}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Département</p>
                                        <p class="text-sm text-gray-900">${member.departement || 'Non défini'}</p>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Date d'entrée</p>
                                        <p class="text-sm text-gray-900">${member.date_entree_formatted}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">Téléphone</p>
                                        <p class="text-sm text-gray-900">${member.telephone || 'Non renseigné'}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h5 class="font-medium mb-3">Solde de congés</h5>
                            <div class="bg-gray-100 p-4 rounded-lg mb-4">
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-600">Congés payés restants</span>
                                    <span class="font-medium">${member.joursRestants} jours</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                                    ${(() => {
                                        const basePaidLeave = 19; // Base allocation without monthly accruals
                                        const percentage = basePaidLeave > 0 ? Math.min(100, (member.joursRestants / basePaidLeave) * 100) : 0;
                                        const colorClass = member.joursRestants > (basePaidLeave * 0.5) ? 'bg-green-600' :
                                                          (member.joursRestants > (basePaidLeave * 0.25) ? 'bg-yellow-500' : 'bg-red-500');
                                        return `<div class="${colorClass} h-2.5 rounded-full" style="width: ${percentage}%"></div>`;
                                    })()}
                                </div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-gray-600">Congés payés pris</span>
                                    <span class="font-medium">${member.joursPris} jours</span>
                                </div>
                                ${member.leaveBalances ? Object.entries(member.leaveBalances).filter(([type]) => type !== 'payé').map(([type, balance]) => `
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-500">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
                                        <span>${balance.remaining}/${balance.total} jours</span>
                                    </div>
                                `).join('') : ''}
                            </div>

                            <h5 class="font-medium mb-3">Statistiques des congés</h5>
                            <div class="bg-gray-100 p-4 rounded-lg mb-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-xs text-gray-500">Total demandes</p>
                                        <p class="text-lg font-semibold">${stats.total || 0}</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">Approuvées</p>
                                        <p class="text-lg font-semibold text-green-600">${stats.approved || 0}</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">Refusées</p>
                                        <p class="text-lg font-semibold text-red-600">${stats.rejected || 0}</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">En attente</p>
                                        <p class="text-lg font-semibold text-yellow-600">${stats.pending || 0}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="border-t pt-5 mt-5 flex justify-between">
                        <a href="/responsable/historique_demandes?employee=${member.id}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-history mr-2"></i> Historique des demandes
                        </a>
                        <a href="/responsable/demandes_approbation?employee=${member.id}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-check mr-2"></i> Voir les demandes
                        </a>
                    </div>
                `;
            } else {
                contentDiv.innerHTML = `
                    <div class="text-center py-10">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Membre non trouvé</h3>
                        <p class="text-gray-500">Impossible de trouver les détails de ce membre.</p>
                    </div>
                `;
            }
        }

        function closeMemberModal() {
            document.getElementById('memberDetailsModal').classList.add('hidden');
        }

        // Filter functions for stat cards
        function filterAvailableMembers() {
            const rows = document.querySelectorAll('#teamMembersTable .member-row');
            rows.forEach(row => {
                const statusCell = row.cells[5]; // Status column
                const statusText = statusCell.textContent.trim();

                // Show only available members (not absent)
                if (statusText.includes('Disponible') || statusText.includes('Congé prochain')) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // Update search input placeholder to indicate filter
            const searchInput = document.getElementById('searchMember');
            searchInput.placeholder = 'Rechercher parmi les membres disponibles...';
            searchInput.value = '';

            // Show clear filter button
            document.getElementById('clearFilterBtn').classList.remove('hidden');
        }

        function filterAbsentMembers() {
            const rows = document.querySelectorAll('#teamMembersTable .member-row');
            rows.forEach(row => {
                const statusCell = row.cells[5]; // Status column
                const statusText = statusCell.textContent.trim();

                // Show only absent members
                if (statusText.includes('Absent')) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // Update search input placeholder to indicate filter
            const searchInput = document.getElementById('searchMember');
            searchInput.placeholder = 'Rechercher parmi les membres absents...';
            searchInput.value = '';

            // Show clear filter button
            document.getElementById('clearFilterBtn').classList.remove('hidden');
        }

        function clearFilter() {
            // Show all rows
            const rows = document.querySelectorAll('#teamMembersTable .member-row');
            rows.forEach(row => {
                row.style.display = '';
            });

            // Reset search input placeholder
            const searchInput = document.getElementById('searchMember');
            searchInput.placeholder = 'Rechercher un membre...';
            searchInput.value = '';

            // Hide clear filter button
            document.getElementById('clearFilterBtn').classList.add('hidden');

            // Remove filter parameter from URL
            const url = new URL(window.location);
            url.searchParams.delete('filter');
            window.history.replaceState({}, '', url);
        }
    </script>
</body>
</html>
